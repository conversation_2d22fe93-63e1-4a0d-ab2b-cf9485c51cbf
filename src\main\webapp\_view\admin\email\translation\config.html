#@jboltLayout()
#define main()
#set(pageId=RandomUtil.random(6))
<div class="jbolt_page" data-key="email_translation_config">
<div class="jbolt_page_title">
<div class="row">
    <div class="col-sm-auto"><h1><i class="jbicon2 jbi-cog"></i>邮件翻译配置</h1></div>
    <div class="col">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-primary btn-sm" onclick="saveConfig()">
                <i class="fa fa-save"></i> 保存配置
            </button>
            <button type="button" class="btn btn-info btn-sm" onclick="testTranslation()">
                <i class="fa fa-flask"></i> 测试翻译
            </button>
            <button type="button" class="btn btn-warning btn-sm" onclick="resetToDefault()">
                <i class="fa fa-refresh"></i> 重置默认
            </button>
            <button type="button" class="btn btn-secondary btn-sm" onclick="viewHistory()">
                <i class="fa fa-history"></i> 变更历史
            </button>
        </div>
    </div>
</div>
</div>

<div class="jbolt_page_content">
    <!-- 配置表单 -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">翻译模型配置</h5>
                </div>
                <div class="card-body">
                    <form id="configForm">
                        <!-- 主要翻译提供商 -->
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">主要翻译提供商</label>
                            <div class="col-sm-4">
                                <select class="form-control" id="primaryProvider" name="primaryProvider" onchange="loadModels('primary')">
                                    <option value="">请选择提供商</option>
                                </select>
                            </div>
                            <div class="col-sm-5">
                                <select class="form-control" id="primaryModel" name="primaryModel">
                                    <option value="">请先选择提供商</option>
                                </select>
                            </div>
                        </div>

                        <!-- 备用翻译提供商 -->
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">备用翻译提供商</label>
                            <div class="col-sm-4">
                                <select class="form-control" id="backupProvider" name="backupProvider" onchange="loadModels('backup')">
                                    <option value="">请选择提供商</option>
                                </select>
                            </div>
                            <div class="col-sm-5">
                                <select class="form-control" id="backupModel" name="backupModel">
                                    <option value="">请先选择提供商</option>
                                </select>
                            </div>
                        </div>

                        <hr>

                        <!-- 重试配置 -->
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">最大重试次数</label>
                            <div class="col-sm-3">
                                <input type="number" class="form-control" id="maxAttempts" name="maxAttempts" min="1" max="10" value="3">
                            </div>
                            <label class="col-sm-3 col-form-label">重试延迟(秒)</label>
                            <div class="col-sm-3">
                                <input type="number" class="form-control" id="delaySeconds" name="delaySeconds" min="1" max="60" value="5">
                            </div>
                        </div>

                        <!-- 功能开关 -->
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">功能开关</label>
                            <div class="col-sm-9">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" id="enableImageTranslation" name="enableImageTranslation" value="true">
                                    <label class="form-check-label" for="enableImageTranslation">启用图片翻译</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" id="enableBatchProcessing" name="enableBatchProcessing" value="true">
                                    <label class="form-check-label" for="enableBatchProcessing">启用批量处理</label>
                                </div>
                            </div>
                        </div>

                        <!-- 其他配置 -->
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">批量处理大小</label>
                            <div class="col-sm-3">
                                <input type="number" class="form-control" id="batchSize" name="batchSize" min="1" max="20" value="5">
                            </div>
                            <label class="col-sm-3 col-form-label">超时时间(秒)</label>
                            <div class="col-sm-3">
                                <input type="number" class="form-control" id="timeoutSeconds" name="timeoutSeconds" min="10" max="300" value="60">
                            </div>
                        </div>

                        <hr>

                        <!-- 提示词配置说明 -->
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">翻译提示词</label>
                            <div class="col-sm-9">
                                <div class="alert alert-info">
                                    <i class="fa fa-info-circle"></i>
                                    翻译提示词由现有的 <strong>AI提示词管理系统</strong> 统一管理。<br>
                                    请前往 <a href="/admin/aiPrompt" target="_blank">AI提示词管理</a> 页面配置 key 为 <code>email_monument_translate</code> 的提示词。
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 配置状态和测试 -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">配置状态</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info" id="configStatus">
                        <strong>当前配置：</strong><br>
                        <span id="currentConfig">加载中...</span>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">翻译测试</h5>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label>测试文本</label>
                        <textarea class="form-control" id="testText" rows="3" placeholder="输入要测试翻译的文本">Hello, this is a test message for translation. Please translate this to Chinese.</textarea>
                    </div>
                    <div class="form-group">
                        <label>选择测试提供商</label>
                        <div class="row">
                            <div class="col-6">
                                <select class="form-control" id="testProvider" onchange="loadTestModels()">
                                    <option value="">选择提供商</option>
                                </select>
                            </div>
                            <div class="col-6">
                                <select class="form-control" id="testModel">
                                    <option value="">选择模型</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary btn-sm btn-block" onclick="runTranslationTest()">
                        <i class="fa fa-play"></i> 运行测试
                    </button>
                    <div id="testResult" class="mt-3" style="display: none;">
                        <div class="alert alert-success">
                            <strong>测试结果：</strong><br>
                            <span id="testResultText"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<!-- 变更历史模态框 -->
<div class="modal fade" id="historyModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">配置变更历史</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped table-sm" id="historyTable">
                        <thead>
                            <tr>
                                <th>配置项</th>
                                <th>旧值</th>
                                <th>新值</th>
                                <th>变更原因</th>
                                <th>变更时间</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

#define js()
<script>
let providers = [];
let currentConfigs = {};

$(document).ready(function() {
    loadProviders();
    loadCurrentConfigs();
});

// 加载提供商列表
function loadProviders() {
    $.get('/admin/email/translation/config/providers', function(data) {
        if (data.state === 'ok') {
            providers = data.data;
            
            // 填充提供商下拉框
            let providerOptions = '<option value="">请选择提供商</option>';
            providers.forEach(function(provider) {
                providerOptions += `<option value="${provider.name}">${provider.name}</option>`;
            });
            
            $('#primaryProvider, #backupProvider, #testProvider').html(providerOptions);
        }
    });
}

// 加载当前配置
function loadCurrentConfigs() {
    $.get('/admin/email/translation/config/datas', function(data) {
        if (data.state === 'ok') {
            data.data.forEach(function(config) {
                currentConfigs[config.config_key] = config.config_value;
            });
            
            // 填充表单
            fillForm();
            updateConfigStatus();
        }
    });
}

// 填充表单
function fillForm() {
    $('#primaryProvider').val(currentConfigs['translation.primary.provider'] || '');
    $('#primaryModel').val(currentConfigs['translation.primary.model'] || '');
    $('#backupProvider').val(currentConfigs['translation.backup.provider'] || '');
    $('#backupModel').val(currentConfigs['translation.backup.model'] || '');
    $('#maxAttempts').val(currentConfigs['translation.retry.max_attempts'] || '3');
    $('#delaySeconds').val(currentConfigs['translation.retry.delay_seconds'] || '5');
    $('#batchSize').val(currentConfigs['translation.batch_size'] || '5');
    $('#timeoutSeconds').val(currentConfigs['translation.timeout_seconds'] || '60');
    
    // 复选框
    $('#enableImageTranslation').prop('checked', currentConfigs['translation.enable_image_translation'] === 'true');
    $('#enableBatchProcessing').prop('checked', currentConfigs['translation.enable_batch_processing'] === 'true');
    
    // 加载模型
    if (currentConfigs['translation.primary.provider']) {
        loadModels('primary');
    }
    if (currentConfigs['translation.backup.provider']) {
        loadModels('backup');
    }
}

// 加载模型列表
function loadModels(type) {
    let providerSelect = type === 'primary' ? '#primaryProvider' : '#backupProvider';
    let modelSelect = type === 'primary' ? '#primaryModel' : '#backupModel';
    let provider = $(providerSelect).val();
    
    if (!provider) {
        $(modelSelect).html('<option value="">请先选择提供商</option>');
        return;
    }
    
    $.get('/admin/email/translation/config/models', {provider: provider}, function(data) {
        if (data.state === 'ok') {
            let modelOptions = '<option value="">请选择模型</option>';
            data.data.forEach(function(model) {
                modelOptions += `<option value="${model.model_identifier}">${model.model_name || model.model_identifier}</option>`;
            });
            $(modelSelect).html(modelOptions);
            
            // 如果是加载当前配置，设置选中值
            if (type === 'primary' && currentConfigs['translation.primary.model']) {
                $(modelSelect).val(currentConfigs['translation.primary.model']);
            } else if (type === 'backup' && currentConfigs['translation.backup.model']) {
                $(modelSelect).val(currentConfigs['translation.backup.model']);
            }
        }
    });
}

// 更新配置状态显示
function updateConfigStatus() {
    let primaryProvider = currentConfigs['translation.primary.provider'] || '未配置';
    let primaryModel = currentConfigs['translation.primary.model'] || '未配置';
    let backupProvider = currentConfigs['translation.backup.provider'] || '未配置';
    let backupModel = currentConfigs['translation.backup.model'] || '未配置';
    
    let statusHtml = `
        主用: ${primaryProvider}/${primaryModel}<br>
        备用: ${backupProvider}/${backupModel}<br>
        重试: ${currentConfigs['translation.retry.max_attempts'] || 3}次<br>
        图片翻译: ${currentConfigs['translation.enable_image_translation'] === 'true' ? '启用' : '禁用'}
    `;
    
    $('#currentConfig').html(statusHtml);
}

// 保存配置
function saveConfig() {
    let formData = $('#configForm').serialize();

    // 手动处理复选框状态，确保未选中的也提交false值
    let enableImageTranslation = $('#enableImageTranslation').is(':checked') ? 'true' : 'false';
    let enableBatchProcessing = $('#enableBatchProcessing').is(':checked') ? 'true' : 'false';

    // 调试信息
    console.log('DEBUG: enableImageTranslation checkbox checked:', $('#enableImageTranslation').is(':checked'));
    console.log('DEBUG: enableBatchProcessing checkbox checked:', $('#enableBatchProcessing').is(':checked'));
    console.log('DEBUG: enableImageTranslation value:', enableImageTranslation);
    console.log('DEBUG: enableBatchProcessing value:', enableBatchProcessing);

    // 移除可能已经存在的这两个参数（避免重复）
    formData = formData.replace(/&?enableImageTranslation=[^&]*/g, '');
    formData = formData.replace(/&?enableBatchProcessing=[^&]*/g, '');

    // 添加明确的复选框状态
    formData += '&enableImageTranslation=' + enableImageTranslation;
    formData += '&enableBatchProcessing=' + enableBatchProcessing;
    formData += '&changeReason=' + encodeURIComponent('管理员通过界面更新配置');

    // 调试信息
    console.log('DEBUG: Final formData:', formData);

    $.post('/admin/email/translation/config/batchUpdate', formData, function(data) {
        if (data.state === 'ok') {
            showToast('配置保存成功', 'success');
            loadCurrentConfigs(); // 重新加载配置
        } else {
            showToast('配置保存失败: ' + data.msg, 'error');
        }
    });
}

// 测试翻译
function testTranslation() {
    let primaryProvider = $('#primaryProvider').val();
    let primaryModel = $('#primaryModel').val();
    
    if (!primaryProvider || !primaryModel) {
        showToast('请先配置主要翻译提供商和模型', 'warning');
        return;
    }
    
    $('#testProvider').val(primaryProvider);
    loadTestModelsWithDefault(primaryProvider, primaryModel);
}

// 加载测试模型（无参数版本，从testProvider获取值）
function loadTestModels() {
    let provider = $('#testProvider').val();
    if (!provider) {
        $('#testModel').html('<option value="">请先选择提供商</option>');
        return;
    }

    $.get('/admin/email/translation/config/models', {provider: provider}, function(data) {
        if (data.state === 'ok') {
            let modelOptions = '<option value="">请选择模型</option>';
            data.data.forEach(function(model) {
                modelOptions += `<option value="${model.model_identifier}">${model.model_name || model.model_identifier}</option>`;
            });
            $('#testModel').html(modelOptions);
        } else {
            $('#testModel').html('<option value="">加载模型失败</option>');
        }
    }).fail(function() {
        $('#testModel').html('<option value="">加载模型失败</option>');
    });
}

// 加载测试模型（带参数版本，用于设置默认值）
function loadTestModelsWithDefault(provider, selectedModel) {
    $.get('/admin/email/translation/config/models', {provider: provider}, function(data) {
        if (data.state === 'ok') {
            let modelOptions = '<option value="">请选择模型</option>';
            data.data.forEach(function(model) {
                modelOptions += `<option value="${model.model_identifier}">${model.model_name || model.model_identifier}</option>`;
            });
            $('#testModel').html(modelOptions);

            if (selectedModel) {
                $('#testModel').val(selectedModel);
            }
        }
    });
}

// 运行翻译测试
function runTranslationTest() {
    let testText = $('#testText').val();
    let provider = $('#testProvider').val();
    let model = $('#testModel').val();
    
    if (!testText || !provider || !model) {
        showToast('请填写完整的测试信息', 'warning');
        return;
    }
    
    $.post('/admin/email/translation/config/testTranslation', {
        testText: testText,
        provider: provider,
        model: model
    }, function(data) {
        if (data.state === 'ok') {
            let result = data.data;
            $('#testResultText').html(`
                <strong>提供商:</strong> ${result.provider}<br>
                <strong>模型:</strong> ${result.model}<br>
                <strong>提示词:</strong> ${result.promptUsed || '默认'}<br>
                <strong>翻译结果:</strong> ${result.translatedText}<br>
                <strong>响应时间:</strong> ${result.responseTime}<br>
                <strong>状态:</strong> ${result.success ? '成功' : '失败'}
            `);
            $('#testResult').show();
        } else {
            showToast('测试失败: ' + data.msg, 'error');
        }
    });
}

// 重置为默认配置
function resetToDefault() {
    if (confirm('确定要重置为默认配置吗？这将覆盖当前所有配置。')) {
        $.post('/admin/email/translation/config/resetToDefault', function(data) {
            if (data.state === 'ok') {
                showToast('配置已重置为默认值', 'success');
                loadCurrentConfigs();
            } else {
                showToast('重置失败: ' + data.msg, 'error');
            }
        });
    }
}

// 查看变更历史
function viewHistory() {
    $.get('/admin/email/translation/config/history', function(data) {
        if (data.state === 'ok') {
            let tbody = $('#historyTable tbody');
            tbody.empty();
            
            data.data.list.forEach(function(record) {
                let row = `
                    <tr>
                        <td>${record.config_key}</td>
                        <td>${record.old_value || '-'}</td>
                        <td>${record.new_value || '-'}</td>
                        <td>${record.change_reason || '-'}</td>
                        <td>${formatDateTime(record.change_time)}</td>
                    </tr>
                `;
                tbody.append(row);
            });
            
            $('#historyModal').modal('show');
        }
    });
}

// 格式化日期时间
function formatDateTime(dateTime) {
    if (!dateTime) return '-';
    return new Date(dateTime).toLocaleString('zh-CN');
}

// 显示提示信息
function showToast(message, type) {
    // 简单的提示实现，可以根据实际UI框架调整
    alert(message);
}
</script>
#end
#end
